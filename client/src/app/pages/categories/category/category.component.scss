.bc_ {
  background-image: linear-gradient(360deg, #f2ead4 4.3%, #d6c28d);
}

.md_chg {
  display: flex;
}

.img_wrp {
  border: 1px solid grey;
  border-radius: 10px;
  overflow: hidden;
}

.save-btn:last-of-type {
  margin: 60px 0 35px 0 !important;
}

.save-btn {
  width: 218px;
  height: 54px;
  padding: 0;
  position: relative;
  margin: 60px 25px 35px 0 !important;

  .btn-backdrop-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: contain;
  }

  .save-btn-label {
    margin: 0 auto;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    padding: 14px 25px;
    color: var(--font-color1);
  }
}

.photo-container {
  z-index: 2;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.7);
  transition: .4s;
}

.carousel-title {
  font-family: Antikvarika;
  font-size: 28px;
  font-weight: 400;
  line-height: 28px;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  max-width: unset;
  margin: auto;
  color: #fff;

  &.gold {
    color: rgba(255, 226, 189, 1);
    margin-bottom: 20px;
  }
}

.frame_w.mirr {
  right: -60px;
}

.frame_w {
  left: -20px;
}

.bcme_img {
  left: 0;
}

.carousel-content {
  position: relative;
  bottom: 25%;
  margin-left: -6%;
  cursor: pointer;
}

.sp_link.medium {
  align-items: center;
  margin-left: unset;
  margin-right: 40px;
}

img.bcm_img {
  height: 280px;
  margin-top: 0;
}

.carousel-item {
  display: flex;
  justify-content: space-between;
  height: unset;
  width: 400px;
  margin-right: 15px;
  margin-left: 15px;
}

.frame_w {
  height: 314px;
  width: 140px;
}

.to-prew-photo {
  flex: 0 0 40%;
  cursor: url(../../../../assets/images/icons/left.webp), move;
}

.to-all-photo {
  flex: 0 0 20%;
  cursor: url(../../../../assets/images/icons/gallery.webp), auto;
}

.to-next-photo {
  flex: 0 0 40%;
  cursor: url(../../../../assets/images/icons/right.webp), pointer;
}

.photo-container-item {
  padding-top: 100px;
}

.photo-folder img {
  width: 140px;
}

.btn-favourite svg {
  width: 20px;
  height: 20px;
}

.btn-favourite.in-favourites svg {
  fill: #ffc94a;
  stroke: none;
}

.show_md {
  display: none;
}

.show_p_md {
  display: none;
  font-family: Prata;
  font-weight: 400;
  font-size: 11px;
  line-height: 11px;
  color: var(--font-color1);
}

.cat_wrap {
  max-width: 930px;
  margin: -35px auto 20px auto;
}

.article-title.ov_wrap {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.wwrap__ {
  width: 100%;
}

.wwrap_ {
  width: 100%;
}

.wwrap {
  max-width: 82%;
}

.articles-search input {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 24px;
  color: var(--font-color1);
  outline: none;
  padding: 5px 315px 5px 85px;
  border: none;
  height: 80px;
  width: 100%;
  background: transparent;
}

.articles-sort {
  display: none;
}

.tags_wrap {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
}

.tag_item_ {
  border-radius: 25px;
  padding: 12px 19px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  color: var(--font-color1);
}

.tag_item_wrapper {
  border: 1px solid var(--book_about);
  // background: linear-gradient(to right, #fff, #fff),
  //   linear-gradient(to right, #FCE1B3, #D19036);
  // background-clip: padding-box,
  //   border-box;
  // background-origin: padding-box,
  //   border-box;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  margin-right: 15px;
}

.articles-tag {
  margin: 25px 0 40px 0;
  justify-content: space-between;
}

.p_filter {
  position: absolute;
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 24px;
  color: rgb(222, 165, 61);
  display: flex;
  align-items: center;
  top: 0;
  right: 170px;
  height: 80px;
  justify-content: center;
  padding-left: 20px;
  cursor: pointer;
}

.p_filter::before {
  content: '';
  background-image: url(assets/images/icons/filter_ls.svg);
  width: 22px;
  height: 22px;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 12px;
}

.articles-search input::placeholder {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 24px;
  color: var(--font-color1);
}

.x_a {
  display: flex;
  background: var(--x_a);
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: 10px;
  cursor: pointer;
}

.articles-search {
  background: url(assets/images/filter_r.svg);
  height: 80px;
  background-position: center !important;
  background-size: contain !important;
  background-repeat: no-repeat !important;
}

.lik_hov:hover {
  .on_hov {
    display: flex;
  }
}

.shr_hov:hover {
  .on_hov {
    display: flex;
  }
}

.fav_hov:hover {
  .on_hov {
    display: flex;
  }
}

.lik_hov {
  position: relative;

  .on_hov {
    left: -85px;
  }

  .on_hov::before {
    right: 14px;
  }
}

.similar-content-section {
  overflow: hidden;
}

.shr_hov {
  position: relative;

  .on_hov {
    left: -47px;
  }
}

.fav_hov {
  position: relative;

  .on_hov {
    left: -30px;
  }

  .on_hov::before {
    left: 19px;
  }
}

.buttn_catg {
  position: relative;
  z-index: 11;
  background: var(--buttn_catg);
  cursor: pointer;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 20px;
  width: 234px;
  height: 50px;
  color: rgba(255, 255, 255, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 70px auto 0 auto;
}

.max_ht {
  max-height: fit-content;
  overflow-y: auto;
  overflow-x: hidden;
  max-width: 460px;
  margin: 0 auto;

  &:focus-visible {
    outline: none;
  }
}

.on_hov {
  display: none;
  position: absolute;
  font-family: Prata;
  font-weight: 400;
  font-size: 15px;
  line-height: 20px;
  height: 50px;
  padding: 0 16px;
  background-color: var(--selection);
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  color: var(--font-color);
  white-space: nowrap;
  top: 33px;
  z-index: 1;
}

.on_hov::before {
  content: '';
  background-image: var(--triangl_w);
  display: block;
  background-repeat: no-repeat;
  background-position: center;
  width: 48px;
  height: 17px;
  position: absolute;
  top: -10px;
}

.article-item {
  padding: 20px 0;
  max-height: 106px;
  border-top: 1px solid rgba(222, 165, 61, 1);
  transition: max-height 0.5s ease-out, padding 0.5s ease-out;

  &.last {
    border-bottom: 1px solid rgba(222, 165, 61, 1);
  }

  .invis_part {
    position: relative;
    opacity: 0;
    padding: 8px 0 0 115px;
    transition: opacity 0.3s ease-out;
    z-index: -1;
  }

  &.widen {
    max-height: 1008px;

    .art_img_::before {
      transform: rotate(180deg);
    }

    .invis_part {
      opacity: 1;
      z-index: 1;
    }
  }
}

.vis_part {
  display: flex;
}

.art_img {
  display: flex;
  align-items: center;
  width: 66px;
  height: 66px;
  border-radius: 50%;
  cursor: pointer;
  position: relative;

  img {
    max-width: 100%;
    width: 66px;
    object-fit: cover;
    height: 100%;
    border-radius: 50%;
    transition: opacity 0.3s ease;
  }

  .image-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(240, 240, 240, 0.9);
    border-radius: 50%;
    z-index: 2;

    app-loading-indicator {
      padding: 0;
    }
  }
}

.art_img_ {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.art_img_::before {
  content: '';
  background-image: var(--arrow_w);
  display: block;
  width: 19px;
  height: 11px;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 12px;
}

.article-category {
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 17px;
  color: var(--text-color);
}

.icons_w {
  margin-top: 5px;
}

.link_more {
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 21px;
  color: var(--font-color1);
  text-decoration: underline;
  margin-top: 20px;
  cursor: pointer;
}

.tag_item {
  border: 1px solid var(--text-color);
  border-radius: 10px;
  padding: 9px 18px;
  font-size: 17px;
  line-height: 19px;
}

.tag_item:hover {
  color: var(--font-color1);
  border: 1px solid var(--blockquote-par);
  background: var(--blockquote-par);
  color: rgba(255, 255, 255, 1);
}

.tags_cont {
  margin-top: 40px;
}

.article-content {
  max-height: 189px;
  overflow: hidden;
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 23px;
  color: var(--font-color1);
}

.article-title {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 26px;
  color: var(--font-color1);
  cursor: pointer;
  margin-bottom: 8px;
}

.titl_w {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 18px;
}

.dropdown-btn {
  border: 1px solid var(--text-color);
  border-radius: 15px;
  width: 460px;
  height: 50px;
  padding: 0 25px 0 15px;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  color: var(--font-color1);
  text-align: left;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.dropdown-btn::after {
  content: "";
  background-image: var(--ar);
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  width: 10px;
  height: 6px;
  position: absolute;
  right: 22px;
}

.custom-dropdown {
  position: absolute;
  height: 80px;
  width: 137px;
  right: 0;
  top: 0;
  cursor: pointer;
}

.dropdown-content {
  display: none;
  border: 1px solid var(--text-color);
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
  border-bottom-left-radius: 15px;
  z-index: 10;
  padding: 16px 0;
  overflow: hidden;
  width: 230px;
  position: relative;
  top: 80px;
  background: rgba(255, 254, 253, 1);
  z-index: 21;
}

.custom-dropdown:hover .dropdown-content,
.custom-dropdown .dropdown-content.open {
  display: block;
}

.dropdown-item {
  padding: 11px 25px;
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  color: var(--font-color1);
  cursor: pointer;
}

.dropdown-item:hover {
  background: var(--selection);
}

.dropdown-item.active {
  background: var(--selection);
}

.actions_w {
  display: flex;
  position: absolute;
  right: 0;
  top: 19px;

  .icons_w {
    margin-left: 20px;

    &.t_p {
      margin-top: 8px;
    }
  }
}

span.text-color:not(.default_) {
  color: var(--text-color);
}

.cal_w {
  margin-left: 33px;

  &.mar_o {
    margin: 0;
  }
}

@media (max-width: 1550px) {
  .bcme_img {
    height: 280px;
  }
}

@media (max-width: 1180px) {
  .dropdown-content {
    width: 185px;
  }
}

@media (max-width: 1050px) {
  .dropdown-content {
    left: -44px;
  }
}

@media (max-width: 950px) {
  .article-title {
    max-width: 434px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    &.wwrap {
      max-width: 65%;
    }
  }

  .article-category {
    &.wwrap {
      max-width: 65%;
    }
  }

  .wwrap_ {
    width: 70%;
  }
}

@media (max-width: 768px) {
  .article-content {
    font-size: 15px;
  }

  .wwrap_ {
    width: 66%;
  }

  .article-title.wwrap {
    max-width: 97%;
  }

  .article-c.article-category {
    max-width: 97%;
  }

  .md_chg {
    display: none;
  }

  .actions_w.show_menu {

    .md_chg {
      display: flex;
    }

    .icon-wrap:not(.no_h) {
      margin-top: 0;
      margin-right: 10px;
    }

    .show_p_md {
      display: flex;
    }

    .icons_w:hover:not(.show_md) {
      background-color: var(--selection);
    }

    .icons_w.is-liked .like_w {
      height: 14px;
      width: 14px;
    }

    .icons_w:hover.in-favourites .star_w {
      height: 14px;
      width: 14px;
    }

    .icons_w:hover .share_w {
      height: 14px;
      width: 14px;
    }

    .icons_w.in-favourites .star_w {
      height: 14px;
      width: 14px;
    }

    .icons_w {
      display: flex;
      align-items: center;
      margin-left: 0;
      padding-left: 14px;
      height: 24px;

      &.separatre_ {
        margin-right: 6px;

        .icon-wrap.like_w {
          svg {
            width: 21px;
            height: 22px;
          }
        }

        .show_p_md {
          display: none;
        }
      }

      &.d__none {
        display: none;
      }

      &.show_md {
        height: unset;
      }

      .list_n {
        width: 14px;
        height: 14px;
      }

      .icon-wrap {
        &.share_w {
          svg {
            width: 14px;
            height: 14px;
          }
        }

        &.star_w:not(.no_h) {
          svg {
            width: 14px;
            height: 14px;
          }
        }
      }

      .icon-wrap.like_w {
        svg {
          width: 14px;
          height: 14px;
        }
      }

      .default_ {
        display: none;
      }
    }
  }

  .md_chg {
    flex-direction: column;
    background-color: #fff;
    position: absolute;
    width: 214px;
    min-height: 122px;
    right: 11px;
    top: 16px;
    z-index: 12;
    border: 1px solid rgb(209 144 54);
    border-top-left-radius: 15px;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
    padding: 13px 0;
  }

  // .md_chg::before {
  //   content: '';
  //   background-image: url(../../../../assets/images/icons/tri__.svg);
  //   background-repeat: no-repeat;
  //   background-position: center;
  //   background-size: contain;
  //   display: flex;
  //   width: 22px;
  //   height: 18px;
  //   position: absolute;
  //   right: -2px;
  //   top: -16px;
  // }

  .article-item .invis_part {
    padding: 21px 0 0 0;
  }

  .link_more {
    font-size: 15px;
    line-height: 15px;
  }

  .icon-wrap.clock_w {
    svg {
      width: 20px;
      height: 20px;
    }
  }

  .icon-wrap.cal_w {
    svg {
      width: 18px;
      height: 20px;
    }
  }

  .icons_w span.text-color {
    font-size: 15px;
    line-height: 15px;
  }

  .tags_cont {
    margin-top: 22px;
  }

  .tag_item {
    padding: 7px 18px;
    font-size: 15px;
    line-height: 15px;
  }

  .icons_w.show_md {
    display: flex !important;
  }

  .dropdown-content {
    left: -74px;
    top: 70px;
  }

  .art_img {
    width: 60px;
    height: 60px;

    img {
      width: 60px;
    }
  }

  .icons_w:not(.t_p, .b_p) {
    display: none;
  }

  .article-category {
    font-size: 15px;
    line-height: 15px;
  }

  .t_p {
    width: 19px;
    height: 20px;
  }

  .article-title {
    font-size: 20px;
    line-height: 20px;
  }

  .dropdown-item {
    padding: 7px 18px;
    font-size: 16px;
    line-height: 24px;
    white-space: nowrap;
  }

  .dropdown-btn::after {
    background-image: var(--arrs_sh);
    height: 17px;
    width: 16px;
    right: unset;
  }

  .dropdown-btn {
    width: 47px;
    height: 38px;
    padding: 0;
    justify-content: center;
  }

  .cat_wrap {
    max-width: 580px;
  }

  .dec_head-title_ {
    font-size: 40px !important;
    padding: 4px 0 !important;
  }

  .dec_head._background {
    margin-top: -137px;
    background-size: 394px;
    height: fit-content;
    padding: 70px 0;

    img {
      max-width: 464px;
    }
  }

  .p_filter::before {
    width: 20px;
    height: 20px;
    background-size: contain;
    margin-right: 10px;
  }

  .dropdown-content {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    padding: 8px 0;
    left: -72%;
  }

  .p_filter {
    font-size: 18px;
    line-height: 18px;
    right: 18%;
  }

  .custom-dropdown {
    width: 15%;
  }

  .articles-search input {
    font-size: 18px;
    line-height: 18px;
    padding: 5px 34% 5px 51px;
  }

  .articles-search input::placeholder {
    font-size: 18px;
    line-height: 18px;
  }

  .articles-search input::placeholder {
    font-size: 18px;
    line-height: 18px;
  }

  .articles-tag {
    margin: 21px 0 40px 0;
  }

  .tag_item_ {
    padding: 7px 19px;
    font-size: 18px;
  }

  .x_a {
    width: 12px;
    height: 12px;
  }

  .tag_item_wrapper {
    margin-right: 10px;
  }

  .article-item {
    padding: 18px 0;
    max-height: 100px;
  }

  .actions_w {
    top: 21px;
  }
}

@media (max-width: 700px) {
  .dropdown-content {
    left: -92%;
  }
}

@media (max-width: 650px) {
  .articles-search input {
    padding: 5px 38% 5px 51px;
  }

  .dropdown-content {
    left: -120%;
  }
}

@media (max-width: 570px) {
  .dropdown-content {
    left: -160%;
  }

  .filter-buttons-container {
    .save-btn {
      width: 188px;
      height: 44px;
      margin: 40px 25px 30px 0 !important;
    }

    .save-btn:last-of-type {
      margin: 40px 0 30px 0 !important;
    }
  }

  .save-btn .save-btn-label {
    font-size: 16px;
    line-height: 16px;
  }

  .icon-wrap.clock_w {
    svg {
      width: 16px;
      height: 16px;
    }
  }

  .icon-wrap.cal_w {
    svg {
      width: 15px;
      height: 16px;
    }
  }

  .icons_w span.text-color {
    font-size: 13px;
    line-height: 13px;
  }

  .tags_cont {
    margin-top: 27px;
  }

  .tag_item {
    padding: 7px 14px;
    font-size: 13px;
    line-height: 13px;
  }

  .art_img {
    width: 44px;
    height: 44px;

    img {
      width: 44px;
    }
  }

  .article-item {
    padding: 16px 0;
    max-height: 84px;
  }

  .article-title {
    max-width: 280px;
    font-size: 18px;
    line-height: 18px;
  }

  .art_img_::before {
    width: 17px;
    height: 10px;
  }

  .actions_w {
    top: 10px;
  }

  .dropdown-item {
    padding: 7px 15px;
    font-size: 17px;
    line-height: 18px;
    line-height: 1.8;
  }

  .articles-tag {
    flex-direction: row-reverse;
    margin-top: 0;
  }

  .tags_wrap {
    flex-wrap: unset;
    overflow-x: auto;
  }

  .dropdown-btn {
    height: 36px;
  }

  .dec_head-title_ {
    font-size: 34px !important;
    line-height: 27px !important;
  }

  .dec_head._background {
    img {
      display: none;
    }
  }

  .dec_head._background {
    margin-top: -145px;
    background-size: 339px;
    padding: 60px 0;
  }

  .articles-search input {
    font-size: 17px;
    line-height: 17px;
  }

  .articles-search input::placeholder {
    font-size: 17px;
    line-height: 17px;
  }

  .p_filter {
    font-size: 16px;
  }

  .dropdown-content {
    top: 62px;
  }

  .tag_item_ {
    padding: 6px 15px;
    font-size: 15px;
  }

  .buttn_catg {
    background-size: contain;
    font-size: 17px;
    line-height: 17px;
    width: 232px;
    height: 40px;
  }

  .p_filter::before {
    width: 16px;
    height: 16px;
  }
}

@media (max-width: 540px) {
  .filter-buttons-container {
    .save-btn {
      width: 150px;
      height: 40px;
      margin: 20px 20px 20px 0 !important;
    }

    .save-btn:last-of-type {
      margin: 20px 0 20px 0 !important;
    }
  }

  .save-btn .save-btn-label {
    font-size: 14px;
    line-height: 14px;
  }
}

@media (max-width: 500px) {
  .dropdown-content {
    left: -195%;
  }

  .articles-search input {
    padding: 5px 42% 5px 9%;
  }
}

@media (max-width: 420px) {
  .article-title {
    max-width: 108px;
  }

  .filter-buttons-container {
    flex-direction: column;
  }

  .filter-buttons-container .save-btn {
    width: 188px;
    margin: 10px auto !important;

    &:last-of-type {
      margin: 10px auto !important;
    }
  }

  .dropdown-content {
    position: absolute;
    padding: 10px 0;
    right: unset;
    left: 0;
    top: 35px;
  }

  .custom-dropdown {
    position: relative;
    height: fit-content;
  }

  .tags_wrap {
    width: 87.2%;
  }

  .articles-search {
    background: url(assets/images/filter_input__.svg);
    background-size: contain !important;
  }

  .articles-sort {
    display: flex;
    margin-right: 10px;
  }

  .articles-sort_ {
    display: none;
  }

  .p_filter {
    right: 10%;

    span {
      display: none;
    }
  }

  .articles-search input {
    padding: 5px 20% 2px 13%;
  }
}

.dropdown-item {
  position: relative;
}

.rotate-up {
  transform: rotate(180deg);
  transition: transform 0.2s ease;
}

.rotate-down {
  transform: rotate(0deg);
  transition: transform 0.2s ease;
}

.sort-arrow {
  position: absolute;
  right: 5px;
  top: 10px;
}
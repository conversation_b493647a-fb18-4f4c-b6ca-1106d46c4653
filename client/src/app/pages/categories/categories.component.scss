@use "category/category.component.scss";

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.categ_wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 13px;

  .catg_wp {
    position: relative;
    overflow: hidden;
    transition: all .3s;
    border-radius: 13px;
    height: 143px;
    margin-bottom: 20px;

    img {
      width: 210px;
      height: 143px;
      border-radius: 13px;
      transition: opacity 0.3s ease;
    }

    .image-loading-indicator {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 13px;
      z-index: 1;

      .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid var(--font-color1, #333);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

.cat-add_w {
  margin-right: 30px;
  margin-bottom: 50px;
}

.hoverable-area {
  cursor: pointer;
  transition: all .3s;
  display: inline-block;
  width: fit-content;
}

.hoverable-area:hover {
  .overlay {
    display: none;
  }

  img {
    scale: 1.2;
    transition: all .3s;
  }

  .category_title {
    color: var(--font-color1) !important;
  }
}

.categories:hover {
  .categ_wrapper {
    .overlay {
      opacity: 1;
    }

    .category_title {
      color: rgb(42, 124, 187);
    }
  }
}

.categories {
  display: flex;
  flex-wrap: wrap;
  margin-right: -32px;
}

.overlay {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 200, 0.2), rgba(255, 165, 0, 0.3));
  pointer-events: none;
  transition: .3s;
}



.category_title {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 26px;
  text-align: center;
  color: var(--font-color1);
  max-width: 201px;
  margin: 0 auto;
}

@media (max-width: 1100px) {
  .categories {
    padding: 0 20px 0 0;
    justify-content: center;
    margin-left: 32px;
  }
}

@media (max-width: 768px) {
  .categories {
    margin-top: -25px;
  }

  .category_title {
    font-size: 20px;
    line-height: 20px;
  }

  .cat-add_w {
    margin-right: 24px;
    margin-bottom: 35px;

    img {
      width: 192px;
      height: 130px;
    }
  }
}

@media (max-width: 510px) {
  .categ_wrapper img {
    width: 150px;
    height: 102px;
  }

  .cat-add_w {
    margin-right: 30px;
    margin-bottom: 28px;
  }

  .category_title {
    font-size: 17px;
    line-height: 17px;
  }

  .categ_wrapper img {
    margin-bottom: 12px;
  }

  .categories {
    padding: 0;
    margin-right: -12px;
    margin-left: 20px;
  }
}

@media (max-width: 420px) {
  .mar_md {
    margin-top: -212px;
  }
}

@media (max-width: 410px) {
  .cat-add_w {
    margin-right: 10px;
  }

  .categories {
    margin-left: 0px;
  }
}
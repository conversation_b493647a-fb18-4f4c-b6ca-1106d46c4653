import {Component, inject} from '@angular/core';
import {ContentService} from "@/services/content.service";
import {NgOptimizedImage} from "@angular/common";
import {environment} from "@/env/environment";
import {TranslocoService} from "@jsverse/transloco";
import {Router} from "@angular/router";
import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component';

@Component({
  selector: 'app-categories',
  standalone: true,
  imports: [
    NgOptimizedImage,
    BreadcrumbComponent
  ],
  templateUrl: './categories.component.html',
  styleUrl: './categories.component.scss'
})
export class CategoriesComponent {
  contentService = inject(ContentService);
  translocoService = inject(TranslocoService);
  router = inject(Router);
  categories: any
  imageLoadingStates: Record<string, boolean> = {};

  ngOnInit() {
    this.contentService.getCategories().subscribe(res => {
      this.categories = res;
      // Initialize loading states for all categories
      res.forEach((category: any) => {
        if (category.preview) {
          this.imageLoadingStates[category.id] = true;
        }
      });
    });
  }

  onImageLoad(categoryId: string) {
    this.imageLoadingStates[categoryId] = false;
  }

  onImageError(categoryId: string) {
    this.imageLoadingStates[categoryId] = false;
  }

  protected readonly environment = environment;
}

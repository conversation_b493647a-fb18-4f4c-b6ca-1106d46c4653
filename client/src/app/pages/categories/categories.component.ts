import {Component, inject} from '@angular/core';
import {ContentService} from "@/services/content.service";
import {NgOptimizedImage} from "@angular/common";
import {environment} from "@/env/environment";
import {TranslocoService} from "@jsverse/transloco";
import {Router} from "@angular/router";
import { BreadcrumbComponent } from '@/components/breadcrumb/breadcrumb.component';
import { LoadingIndicatorComponent } from '@/components/loading-indicator/loading-indicator.component';

@Component({
  selector: 'app-categories',
  standalone: true,
  imports: [
    NgOptimizedImage,
    BreadcrumbComponent,
    LoadingIndicatorComponent
  ],
  templateUrl: './categories.component.html',
  styleUrl: './categories.component.scss'
})
export class CategoriesComponent {
  contentService = inject(ContentService);
  translocoService = inject(TranslocoService);
  router = inject(Router);
  categories: any
  imageLoadingStates: Record<string, boolean> = {};

  ngOnInit() {
    this.contentService.getCategories().subscribe(res => {
      this.categories = res;
    });
  }

  onImageLoad(categoryId: string) {
    // Add a small delay to ensure spinner is visible
    setTimeout(() => {
      this.imageLoadingStates[categoryId] = false;
    }, 300);
  }

  onImageError(categoryId: string) {
    this.imageLoadingStates[categoryId] = false;
  }

  protected readonly environment = environment;
}

import { CommonModule, isPlatformBrowser, NgOptimizedImage } from '@angular/common';
import { Component, Inject, Input, PLATFORM_ID } from '@angular/core';
import { interval, Subscription } from 'rxjs';
import { environment } from '@/env/environment';
import { CarouselContent } from '@/interfaces/carouselItemContent';
import { LoadingIndicatorComponent } from '../loading-indicator/loading-indicator.component';

@Component({
  selector: 'app-carousel',
  standalone: true,
  imports: [CommonModule, NgOptimizedImage, LoadingIndicatorComponent],
  templateUrl: './carousel.component.html',
  styleUrl: './carousel.component.scss'
})
export class CarouselComponent {
  @Input() slidersConstructorData!: CarouselContent;
  protected readonly environment = environment;
  @Input() interval: number = 2000;
  currentIndex = 0;
  innerWidth: number = 1920;
  autoSlideSubscription: Subscription | undefined;
  imageLoadingStates: Record<string, boolean> = {};
  constructor(
    @Inject(PLATFORM_ID) private platformId: any
  ) { }

  ngOnInit(): void {
    if (isPlatformBrowser(this.platformId)) {
      this.innerWidth = window.innerWidth;
      window.addEventListener('resize', this.handleResize.bind(this));
    }
    this.startAutoSlide();
  }

  ngAfterViewInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.innerWidth = window.innerWidth;
    }
  }

  ngOnDestroy(): void {
    this.stopAutoSlide();
    if (isPlatformBrowser(this.platformId)) {
      window.removeEventListener('resize', this.handleResize.bind(this));
    }
  }

  prevSlide() {
    if (this.slidersConstructorData.size == "small") {
      const itemsPerGroup = this.getItemsPerGroup();
      const totalGroups = Math.ceil(this.slidersConstructorData.items.length / itemsPerGroup);
      this.currentIndex = (this.currentIndex === 0) ? totalGroups - 1 : this.currentIndex - 1;
    } else {
      this.currentIndex = (this.currentIndex === 0) ? this.slidersConstructorData.items.length - 1 : this.currentIndex - 1;
    }
  }

  nextSlide() {
    if (this.slidersConstructorData.size == "small") {
      const itemsPerGroup = this.getItemsPerGroup();
      const totalGroups = Math.ceil(this.slidersConstructorData.items.length / itemsPerGroup);
      this.currentIndex = (this.currentIndex + 1) % totalGroups;
    } else {
      this.currentIndex = (this.currentIndex + 1) % this.slidersConstructorData.items.length;
    }
  }

  openUrl(link: string) {
    if (isPlatformBrowser(this.platformId)) {
      window.open(link);
    }
  }

  getItemsPerGroup(): number {
    if (isPlatformBrowser(this.platformId)) {
      this.innerWidth = window.innerWidth;
      if ((window.innerWidth <= 1079) && (window.innerWidth > 768)) {
        return 2;
      } else if (window.innerWidth <= 768) {
        return 1;
      }
      return 3;
    }
    return 3;
  }

  handleResize(): void {
    this.currentIndex = Math.min(this.currentIndex, Math.ceil(this.slidersConstructorData.items.length / this.getItemsPerGroup()) - 1);
  }

  startAutoSlide(): void {
    if (isPlatformBrowser(this.platformId) && this.slidersConstructorData) {
      this.autoSlideSubscription = interval(this.interval).subscribe(() => {
        const itemsPerGroup = this.getItemsPerGroup();
        const totalGroups = Math.ceil(this.slidersConstructorData.items.length / itemsPerGroup);
        this.currentIndex = (this.currentIndex + 1) % totalGroups;
      });
    }
  }

  stopAutoSlide(): void {
    if (this.autoSlideSubscription) {
      this.autoSlideSubscription.unsubscribe();
    }
  }

  pauseAutoSlide(): void {
    this.stopAutoSlide();
  }

  resumeAutoSlide(): void {
    this.startAutoSlide();
  }

  onCarouselImageLoad(itemId: string) {
    setTimeout(() => {
      this.imageLoadingStates[itemId] = false;
    }, 300);
  }

  onCarouselImageError(itemId: string) {
    this.imageLoadingStates[itemId] = false;
  }
}

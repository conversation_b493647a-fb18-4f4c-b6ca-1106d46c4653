<div class="m_btn wide relative" *ngIf="slidersConstructorData.title">
    <span>
        {{slidersConstructorData.title}}
    </span>
</div>
@if ((slidersConstructorData.size == "small") || (slidersConstructorData.size == "middle" &&innerWidth <= 950)) {
<div class="relative sp_carousel">
    <div class="carousel-container trp">
        <div class="carousel" [ngStyle]="{'transform': 'translateX(-' + currentIndex * 100 + '%)'}">
            <div class="carousel-item" *ngFor="let item of slidersConstructorData.items">
                @if (item && item.show) {
                <div (click)="openUrl(item.link)" class="frame_"></div>
                <a class="sp_link" [href]="item.link">
                    <!-- Loading indicator for carousel images -->
                    <div *ngIf="item.image && item.id && imageLoadingStates[item.id.toString()] !== false" class="image-loading-overlay">
                      <app-loading-indicator size="normal"></app-loading-indicator>
                    </div>
                    <img *ngIf="item.image" class="bcr_img" width="321" height="300" [ngSrc]="environment.serverUrl + '/upload/' + item.image.name"
                    [alt]="item.text"
                    (load)="item.id && onCarouselImageLoad(item.id.toString())"
                    (error)="item.id && onCarouselImageError(item.id.toString())"
                    [style.opacity]="item.id && imageLoadingStates[item.id.toString()] === false ? '1' : '0'">
                    <div class="carousel-content">
                        <div class="carousel-title clamp_2">{{ item.text }}</div>
                        <div class="carousel-date">
                            <img alt="date" width="220" height="51" loading="lazy" ngSrc="assets/images/date.webp">
                            <span class="absolute">{{ (slidersConstructorData.size == "middle" && innerWidth <= 768) ? 'Подробнее' : item.date }}</span>
                        </div>
                    </div>
                </a>
                }
            </div>
        </div>
    </div>
    <button title="Влево" class="carousel-arrow left-arrow" (click)="prevSlide()"></button>
    <button title="Вправо" class="carousel-arrow right-arrow" (click)="nextSlide()"></button>
</div>
}

@if (slidersConstructorData.size == "middle" && innerWidth > 950) {
<div class="relative sp_carousel idle_s">
    <div class="carousel-container middle_s">
        <div class="carousel" [ngStyle]="{'transform': 'translateX(-' + currentIndex * (innerWidth <= 1079 ? 100 : 60) + '%)'}">
            <div class="carousel-item relative" *ngFor="let item of slidersConstructorData.items">
                @if (item && item.show) {
                <img (click)="openUrl(item.link)" class="frame_w dec" width="144" height="285" loading="lazy" ngSrc="assets/images/venzel_withdecor.webp" alt="frame">
                <a class="sp_link medium relative" [href]="item.link">
                    <img *ngIf="item.image" class="bcm_img" width="415" height="250" [ngSrc]="environment.serverUrl + '/upload/' + item.image.name"
                    [alt]="item.text">
                    <div class="absolute bcmb_img"></div>
                    <div class="absolute bcme_img"></div>
                    <div class="absolute bcme_img rt"></div>
                    <div class="carousel-content">
                        <div class="carousel-title clamp_2 gold">{{ item.text }}</div>
                        <div class="carousel-date mb_">
                            <img alt="shine" width="144" height="285" loading="lazy" ngSrc="assets/images/shine.webp">
                            <span class="absolute">Подробнее</span>
                        </div>
                    </div>
                </a>
                <img (click)="openUrl(item.link)" class="frame_w mirr dec" width="144" height="285" loading="lazy" ngSrc="assets/images/venzel_withdecor.webp" alt="frame">
                }
            </div>
        </div>
    </div>
    <button title="Влево" class="carousel-arrow left-arrow" (click)="prevSlide()"></button>
    <button title="Вправо" class="carousel-arrow right-arrow" (click)="nextSlide()"></button>
</div>
}

@if (slidersConstructorData.size == "large") {
<div class="relative sp_carousel">
    <div class="carousel-container middle">
        <div class="carousel" [ngStyle]="{'transform': 'translateX(-' + currentIndex * (innerWidth <= 1079 ? 100 : 75) + '%)'}">
            <div class="carousel-item" *ngFor="let item of slidersConstructorData.items">
                @if (item && item.show) {
               <img (click)="openUrl(item.link)" class="frame_w" width="69" height="320" loading="lazy" ngSrc="assets/images/venzel_left.webp" alt="frame">
                <a class="sp_link medium relative" [href]="item.link">
                    <img *ngIf="item.image" class="bcm_img large_" width="759" height="280" [ngSrc]="environment.serverUrl + '/upload/' + item.image.name"
                    [alt]="item.text">
                    <div class="absolute bcmb_img"></div>
                    <div class="absolute bcme_img"></div>
                    <div class="absolute bcme_img rt"></div>
                    <div class="carousel-content">
                        <div class="carousel-title gold clamp_2">{{ item.text }}</div>
                        <div class="carousel-date mb_">
                            <img alt="date" width="346" height="57" loading="lazy" ngSrc="assets/images/date.webp">
                            <span class="absolute">Подробнее</span>
                        </div>
                    </div>
                </a>
                <img (click)="openUrl(item.link)" class="frame_w mirr" width="69" height="320" loading="lazy" ngSrc="assets/images/venzel_left.webp" alt="frame">
                }
            </div>
        </div>
    </div>
    <button title="Влево" class="carousel-arrow left-arrow" (click)="prevSlide()"></button>
    <button title="Вправо" class="carousel-arrow right-arrow" (click)="nextSlide()"></button>
</div>
}

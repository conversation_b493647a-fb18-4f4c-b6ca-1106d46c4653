.bcr_img {
    object-fit: cover;
    max-height: 300px;
}

img.bcr_img {
    height: inherit;
}

img.bcm_img {
    height: revert-layer;
}

.sp_link {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
    position: relative;

    .image-loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg,
          var(--light-color, rgba(249, 233, 200, 0.95)) 0%,
          var(--selection, rgba(255, 226, 163, 0.95)) 50%,
          var(--pl-stop, rgba(240, 187, 86, 0.95)) 100%);
        backdrop-filter: blur(4px);
        border-radius: 10px;
        z-index: 2;

        app-loading-indicator {
            padding: 0;
        }
    }
}

.carousel-container.middle_s .carousel-item {
    min-width: 40%;

    .carousel-content {
        width: 100%;
    }

    .carousel-date.mb_ {
        margin-bottom: -26px;
    }

    .frame_w {
        position: absolute;
    }

    .bcme_img.rt {
        height: 250px;
        width: 140px;
    }

    .bcme_img:not(.rt) {
        width: 176px;
        height: 414px;
        left: 118px;
        bottom: -71px;
        border-top: none;
        border-bottom: none;
        transform: rotate(-90deg);
        background: linear-gradient(-90deg, rgba(9, 80, 126, 0) 0%, #09507E 140%);
    }

    .bcmb_img {
        height: 250px;
        background: linear-gradient(-90deg, rgba(9, 80, 126, 0) 0%, #09507E 100%);
    }
}

.bcmb_img {
    width: 130px;
    height: 280px;
    left: 0;
    background: rgba(9, 80, 126, 1);
    border-top: 3px solid #CBAB89;
    border-bottom: 3px solid #CBAB89;
}

.bcme_img {
    width: 176px;
    height: 280px;
    left: 130px;
    background: linear-gradient(-90deg, rgba(9, 80, 126, 0) 0%, #09507E 100%);
    border-top: 3px solid #CBAB89;
    border-bottom: 3px solid #CBAB89;

    &.rt {
        left: unset;
        right: 0;
        transform: rotate(180deg);
    }
}

.sp_link.medium {
    align-items: unset;
    margin-left: -30px;
    margin-right: -30px;
}

.carousel-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 382px;
}

.sp_carousel {
    max-width: 90%;
    margin: auto;
}

@media (max-width: 1550px) {
    .idle_s {
        .carousel-arrow.right-arrow {
            right: 30px;
        }

        .carousel-arrow.left-arrow {
            left: 50px;
        }
    }

    .carousel-container.middle_s .carousel-item {
        .carousel-content .carousel-title {
            font-size: 20px;
        }

        .carousel-content .carousel-title.gold {
            margin-bottom: 14px;
        }

        .carousel-date.mb_ {
            margin-bottom: -52px;
        }

        .bcm_img {
            width: 365px;
        }

        .bcme_img:not(.rt) {
            width: 170px;
            height: 357px;
            left: 100px;
            bottom: -45px;
        }
    }

    .bcr_img {
        width: 260px;
        max-height: 240px;
    }

    .carousel-container.middle {
        margin: auto;

        .carousel-item {
            padding: 0 41px 0 0;
        }
    }

    .bcm_img {
        width: 580px;
        height: 230px;
    }

    .bcm_img.large_ {
        width: 580px;
        height: 230px;
    }

    .bcmb_img {
        width: 108px;
        height: 230px;
    }

    .bcme_img {
        height: 230px;
        left: 106px;
    }
}

@media (max-width: 1400px) {
    .sp_carousel {
        max-width: unset;
    }
}

@media (max-width: 1239px) {
    .carousel-container.middle_s .carousel-item {
        .bcmb_img {
            height: 215px;
        }

        .carousel-content {
            bottom: 98px;
        }

        .frame_w {
            height: 235px;
        }

        .bcm_img {
            width: 315px;
            height: 215px;
        }

        .bcme_img.rt {
            height: 215px;
        }

        .bcme_img:not(.rt) {
            width: 170px;
            height: 314px;
            left: 70px;
            bottom: -6px;
        }
    }
}

@media (max-width: 1079px) {
    .carousel-container.middle .carousel-item {
        min-width: 100%;
        padding: 0;
    }

    .carousel-container.middle_s {
        .carousel-item .bcm_img {
            width: 250px;
        }

        .carousel-item .bcme_img:not(.rt) {
            width: 115px;
            height: 240px;
            bottom: 5px;
        }

        max-width: 755px;
    }
}

@media (max-width: 500px) {
    .bcr_img {
        width: 230px;
        max-height: 212px;
    }
}
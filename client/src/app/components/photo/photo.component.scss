@import url(../carousel/carousel.component.scss);

.image-items {
    flex-wrap: wrap;
    max-width: 1305px;
    margin: auto;
    text-align: center;
}

.bc_ {
    background-image: linear-gradient(360deg, #f2ead4 4.3%, #d6c28d);
}

.img_wrp {
    border: 1px solid grey;
    border-radius: 10px;
    overflow: hidden;
}

.photo-folder img {
    width: 140px;
}

.image-items img {
    height: 300px;
    object-fit: cover;
}

.btn-favourite svg {
    width: 20px;
    height: 20px;
}

.btn-favourite.in-favourites svg {
    fill: #ffc94a;
    stroke: none;
}


.cat_wrap {
    max-width: 930px;
    margin: -35px auto 20px auto;
}

.folders_wrap {
    display: flex;
    justify-content: left;
    flex-wrap: wrap;
    max-width: 930px;
    margin: 0 auto;
    column-gap: 30px;
    row-gap: 45px;

    .folder-item {
        flex-basis: calc(33.33% - 20px);
        max-width: calc(33.33% - 20px);
        min-height: 330px;
        position: relative;
        display: flex;
        flex-direction: column;
        gap: 15px;
        align-items: center;
        justify-content: flex-start;
        overflow: hidden;

        .folder-img {
            border-radius: 20px;
            max-width: 290px;
            max-height: 290px;
            min-height: 290px;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.3s ease;
        }

        .image-loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 290px;
            height: 290px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(240, 240, 240, 0.9);
            border-radius: 20px;
            z-index: 2;

            app-loading-indicator {
                padding: 0;
            }
        }

        .folder-title {
            font-family: Prata;
            font-weight: 400;
            font-style: Regular;
            font-size: 20px;
            leading-trim: NONE;
            line-height: 24px;
            letter-spacing: 0;
            text-align: center;
            color: var(--text-color);
        }
    }
}

svg.is-liked path {
  fill: white!important;
}

@media (max-width: 1250px) {
    .folders_wrap {
        row-gap: 30px;
        column-gap: 20px;

        .folder-item {
            flex-basis: calc(33.33% - 14px);
            max-width: calc(33.33% - 14px);
        }
    }
}

@media (max-width: 1100px) {
    .folders_wrap {
        max-width: 605px;
        padding-top: 40px;

        .folder-item {
            min-height: 222px;
            gap: 20px;

            .folder-img {
                border-radius: 15px;
                max-width: 180px;
                max-height: 180px;
                min-height: 180px;
            }

            .folder-title {
                font-size: 18px;
                line-height: 22px;
            }
        }
    }

    app-photo-gallery {
        padding: 40px 7px 0;
        display: block;
    }
}

@media (max-width: 640px) {
    .photo-item-info {
        position: fixed;
        bottom: 48px;
        left: 1vw;
        max-width: 98vw;
        width: 98vw;
        gap: 10px;
        padding: 40px 0;

        .photo-item-description {
            font-size: 15px;
            line-height: 19px;

        }

        .photo-item-actions {
            svg {
                zoom: 0.8;
            }
        }
    }

}

@media (max-width: 768px) {
    .dec_head-title_ {
        font-size: 40px !important;
        padding: 4px 0 !important;
    }

    .dec_head._background {
        margin-top: -137px;
        background-size: 394px;
        height: fit-content;
        padding: 70px 0;

        img {
            max-width: 464px;
        }
    }

    .cat_wrap {
        max-width: 580px;
    }
}

@media (max-width: 700px) {
    .folders_wrap {
        max-width: 500px;

        .folder-item {
            min-height: 191px;

            .folder-img {
                border-radius: 10px;
                max-width: 160px;
                max-height: 160px;
                min-height: 160px;
            }

            .folder-title {
                font-size: 16px;
                line-height: 19px;
            }
        }
    }
}

@media (max-width: 640px) {
    .photo-item-info {
        position: fixed;
        bottom: 10px;
        left: 1vw;
        max-width: 98vw;
        width: 98vw;
        gap: 10px;
        padding: 40px 0 80px;

        .photo-item-description {
            font-size: 13px;
            line-height: 17px;
            color: #fff;
            max-width: 95vw;
        }
    }

}

@media (max-width: 570px) {
    .dec_head-title_ {
        font-size: 34px !important;
        line-height: 27px !important;
    }

    .dec_head._background {
        img {
            display: none;
        }
    }

    .dec_head._background {
        margin-top: -145px;
        background-size: 339px;
        padding: 60px 0;
    }

    .folders_wrap {
        max-width: 330px;
        row-gap: 30px;
        column-gap: 10px;

        .folder-item {
            gap: 12px;
            flex-basis: calc(50% - 5px);
            max-width: calc(50% - 5px);
        }
    }

}

<div>
  <div class="middle_stripe">
    <breadcrumb></breadcrumb>
    <div class="wrapper_line">
      <div class="dec_head _background">
        <img class="carn_b_" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
        <h1 class="dec_head-title_">{{ h1 ? h1 : 'Фотогалерея'}}</h1>
        <img class="carn_b_ rotate" width="862" height="84" alt="icon" ngSrc="assets/images/top_drawing.svg" />
      </div>
      <div class="cat_wrap">
        <!-- <div class="flex p-4 relative flex-wrap justify-center">
          
        </div> -->
        @if(!id) {
        <div class="folders_wrap">
          @for(photo of photos; track photo.id) {
          <div class="folder-item relative cursor-pointer"
            (click)="$event.stopPropagation();router.navigate(['/'+translocoService.getActiveLang()+'/photo/' + photo.slug])">
            <!-- Loading indicator for photo images -->
            @if(photo.cover && !photo.imageError && imageLoadingStates[photo.id.toString()] !== false) {
            <div class="image-loading-overlay">
              <app-loading-indicator size="normal"></app-loading-indicator>
            </div>
            }
            <img
              [ngSrc]="photo.cover && !photo.imageError ? environment.serverUrl + '/upload/' + photo.cover.name : 'assets/images/meadow.avif'"
              width="290" height="290"
              (error)="onImageError(photo)"
              (load)="onPhotoImageLoad(photo.id.toString())"
              [alt]="photo.title"
              class="folder-img"
              [style.opacity]="photo.cover && !photo.imageError && imageLoadingStates[photo.id.toString()] === false ? '1' : (photo.cover && !photo.imageError ? '0' : '1')">
            <div class="folder-title">{{photo.title}}</div>
          </div>
          }
        </div>
        } @else {
        <app-photo-gallery 
          [images]="images" 
          [actionsSettings]="actionsSettings" 
          [favourites]="favourites"
          ></app-photo-gallery>
          <!-- (onPhotoClick)="showPhoto($event)"  -->
        <!-- <div class="image-items flex flex-row w-full h-full">
                @for(item of images; track $index) {
                    <div class="flex flex-col w-fit h-fit mb-3 mr-6 cursor-pointer img_wrp" #blockRef>
                      <div (click)="$event.stopPropagation();showPhoto(item)">
                        <div class="flex flex-col max-w-fit">
                          <img #imageRef (load)="setBlockWidth($index)" [src]="environment.serverUrl + '/upload/' + item['name']" alt="i">
                        </div>
                        <span class="flex items-center justify-between p-2 bc_">
                        
                          <span class="max-w-[70%] mr-auto text-left">{{ item['description'] | slice:0:60 }}{{ item['description']?.length > 60 ? '...' : '' }}</span>
                          <button *ngIf="this.profileService.profile" class="btn-favourite mr-3" [ngClass]="{'in-favourites': inFavourites(item.id)  }" (click)="$event.stopPropagation();favorites(item.id)">
                            <svg fill="transparent" stroke="gray" stroke-width="20px" height="800px" width="800px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                 viewBox="0 0 489.914 489.914" xml:space="preserve">
                              <path d="M483.871,204.522c5.38-4.725,7.395-12.197,5.123-18.98c-2.272-6.785-8.394-11.538-15.53-12.07l-142.249-10.618
                              c-6.607-0.502-12.376-4.609-14.982-10.682L259.738,21.184c-2.838-6.555-9.331-10.793-16.484-10.714
                              c-7.137,0.066-13.549,4.401-16.259,11.021l-54.056,132.016c-2.497,6.125-8.204,10.346-14.792,10.956l-142.04,13.245
                              c-7.117,0.66-13.146,5.527-15.29,12.343c-2.142,6.817,0.017,14.263,5.461,18.884l108.845,92.216
                              c5.046,4.288,7.299,11.005,5.851,17.452L89.682,457.786c-1.565,6.978,1.19,14.212,7.023,18.369c5.834,4.141,13.566,4.4,19.658,0.627
                              l121.315-75.039c5.624-3.477,12.715-3.545,18.417-0.159l122.686,72.767c6.14,3.642,13.888,3.256,19.624-0.998
                              c5.738-4.254,8.381-11.555,6.671-18.496l-33.839-138.575c-1.579-6.43,0.547-13.198,5.511-17.561L483.871,204.522z"/>
                            </svg>
                          </button>
                        </span>
                      </div>
                    </div>
                }
            </div> -->
        }
      </div>
    </div>
  </div>
</div>